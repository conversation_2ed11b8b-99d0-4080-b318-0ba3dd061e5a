import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/services/cache_invalidation_service.dart';
import 'package:koc_app/src/shared/services/url_helpers.dart';
import '../../../shared/utils/handle_error.dart';

class TrafficSourcesCubit extends BaseCubit<SocialInfo> {
  final AccountRepository _accountRepository;
  TrafficSourcesCubit(this._accountRepository) : super(const SocialInfo());

  void emitSocialInfo(SocialInfo socialInfo) {
    emit(socialInfo);
  }

  void updateFollowerNumber(FollowerNumber followerNumber) {
    emit(state.copyWith(totalFollowerLevel: followerNumber));
  }

  void updateId(int id) {
    emit(state.copyWith(id: id));
  }

  void updateSocialType(SocialType socialMediaType) {
    emit(state.copyWith(socialMediaType: socialMediaType));
  }

  Future<bool> upsertSocialInfo(SocialInfo socialInfo) async {
    try {
      final type = getSocialTypeFromUrl(socialInfo.url);
      var updatedSource = socialInfo.copyWith(socialMediaType: type);
      if (type == SocialType.OTHER) {
        updatedSource = updatedSource.copyWith(
          totalFollowerLevel: FollowerNumber.EMPTY,
        );
      }
      final siteId = await _accountRepository.updateTrafficSources(updatedSource);

      // Use cache invalidation service for site updates
      await CacheInvalidationService().invalidateSiteCache(specificSiteId: siteId);

      emit(state.copyWith(
        id: siteId,
        socialMediaType: type,
        totalFollowerLevel: updatedSource.totalFollowerLevel,
      ));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
    return false;
  }

  Future<bool> deleteSocialInfo(int id) async {
    try {
      await _accountRepository.deleteTrafficSources(id);

      // Use cache invalidation service for site deletion
      await CacheInvalidationService().invalidateSiteCache(specificSiteId: id);

      final sites = await commonCubit.sharedPreferencesService.getSites();
      final updatedSites = sites.where((site) => site.id != id).toList();
      await commonCubit.sharedPreferencesService.setSites(updatedSites);
      final siteCubit = Modular.get<SiteCubit>();
      await siteCubit.reloadSites();
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }
}
