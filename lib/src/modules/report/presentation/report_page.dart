import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_summary.dart';
import 'package:koc_app/src/modules/report/data/model/payment/minimum_payment_details.dart';
import 'package:koc_app/src/modules/report/data/model/performance_monthly_report_data.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/charts/campaign_chart.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_models.dart';
import 'package:koc_app/src/shared/widgets/charts/dual_axis_line_chart.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class ReportPage extends StatefulWidget {
  const ReportPage({super.key});

  @override
  State<ReportPage> createState() => _ReportPageState();
}

class _ReportPageState extends BasePageState<ReportPage, ReportCubit> with ReportMixin, CommonMixin {
  @override
  void initState() {
    initData();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _checkAndRefreshIfNeeded();
  }

  Future<void> _checkAndRefreshIfNeeded() async {
    final currentSiteId = await cubit.commonCubit.sharedPreferencesService.getCurrentSiteId();
    if (currentSiteId != null && currentSiteId != cubit.state.selectedSiteId) {
      await _loadDataWithCacheFirst();
    }
  }

  Future<void> _loadDataWithCacheFirst() async {
    cubit.startSiteSwitching();

    try {
      await cubit.findReportData();
      cubit.endSiteSwitching();
    } catch (e) {
      cubit.showLoading();
      await Future.wait([cubit.findReportData(), Future.delayed(const Duration(milliseconds: 500))]);
      cubit.hideLoading();
      cubit.endSiteSwitching();
    }
  }

  Future<void> initData() async {
    cubit.startSiteSwitching();
    cubit.showLoading();

    await Future.wait([cubit.findReportData(), Future.delayed(const Duration(milliseconds: 500))]);

    cubit.hideLoading();
    cubit.endSiteSwitching();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: buildSiteSelectionTitle(context, 'Report', onTap: initData),
        showNotificationAction: true,
        showBottomDivider: false,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<ReportCubit, ReportState>(
      bloc: cubit,
      builder: (_, state) {
        return Stack(
          children: [
            PullToRefreshWrapper(
              onRefresh: () => cubit.pullToRefresh(),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    spacing: 16.r,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPerformance(state),
                      _buildConversions(state),
                      _buildCampaign(state),
                      _buildPayment(state),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPayment(ReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Payment', () {
          Modular.to.pushNamed('/report/payment');
        }),
        _buildPaymentContent(state),
      ],
    );
  }

  Widget _buildPaymentContent(ReportState state) {
    final paymentSummary = state.paymentSummary ?? PaymentSummary();
    final minimumPaymentDetails = state.minimumPaymentDetails ?? MinimumPaymentDetails();
    final currency = state.currency;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: ColorConstants.borderColor, width: 1.r),
      ),
      child: Column(
        children: [
          _buildCardContent(
            Text(
              paymentSummary.lifetimeTotalPaidReward.toPrice(currency),
              style: context.textBodySmall(fontWeight: FontWeight.w500),
            ),
            'Your lifetime reward',
            'Total reward paid to you since registration with ACCESSTRADE.',
          ),
          const Divider(color: Color(0xFFE7E7E7)),
          _buildCardContent(
            ClipRRect(
              borderRadius: BorderRadius.circular(9999),
              child: Stack(
                children: [
                  LinearProgressIndicator(
                    color:
                        paymentSummary.availablePayment < minimumPaymentDetails.minimumAmount
                            ? const Color(0xFFFFB522)
                            : const Color(0xFF1AAA55),
                    backgroundColor: Colors.grey[400],
                    value:
                        minimumPaymentDetails.minimumAmount > 0
                            ? paymentSummary.availablePayment / minimumPaymentDetails.minimumAmount
                            : 0.0,
                    minHeight: 28.r,
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    height: 28.r,
                    padding: EdgeInsets.only(left: 12.r),
                    child: Text(
                      paymentSummary.availablePayment.toPrice(currency),
                      style: Theme.of(
                        context,
                      ).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
            'Minimum payment amount (${minimumPaymentDetails.minimumAmount.toPrice(minimumPaymentDetails.currency.isNotEmpty ? minimumPaymentDetails.currency : currency)})',
            state.country != null
                ? 'Minimum payment for ${state.country!.name.toTitleCase()} bank account is ${state.country!.localCurrencyCode} ${minimumPaymentDetails.minimumAmount.toCommaSeparated()}, International bank is ${state.country!.internationalCurrencyCode} ${state.country!.internationalMinimumPayment.toCommaSeparated()}'
                : 'Minimum payment amount required to process payment',
          ),
        ],
      ),
    );
  }

  Widget _buildCampaign(ReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Campaign', () {
          Modular.to.pushNamed('/report/campaign');
        }),
        _buildCampaignChart(state),
      ],
    );
  }

  Widget _buildCampaignChart(ReportState state) {
    final List<CampaignChartItem> chartItems =
        state.topTenCampaignsClickCount.isNotEmpty
            ? state.topTenCampaignsClickCount.map((e) {
              return CampaignChartItem(name: e.campaignName, value: e.clicks);
            }).toList()
            : [];

    final chartData = CampaignChartData(items: chartItems, valueLabel: 'Clicks', nameLabel: 'Campaign');

    const chartConfig = CampaignChartConfig(
      chartType: CampaignChartType.horizontalBar,
      title: 'Top 10 Campaigns sorted by Clicks',
      showTitle: true,
      aspectRatio: 1.5,
      maxItems: 10,
      yAxisGridLines: 4,
      showGrid: true,
      sortAscending: true,
      normalHeight: 300.0,
      emptyHeight: 200.0,
      showContainer: true,
      xAxisLabelInterval: 1,
    );

    return CampaignChart(data: chartData, config: chartConfig);
  }

  Widget _buildConversions(ReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Conversion', () {
          Modular.to.pushNamed('/report/conversion');
        }),
        _buildConversion(state),
      ],
    );
  }

  Widget _buildConversion(ReportState state) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
      ),
      child: Column(
        children: [
          _buildCardContent(
            Text(
              state.thisMonthOccurredConversionCount.toCommaSeparated(),
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
            ),
            'Conversions occurred this month',
            'Saldo yang akan dibawa ke bulan berikutnya. Apabila tidak ada maka akan bernilai 0',
          ),
          const Divider(color: Color(0xFFE7E7E7)),
          _buildCardContent(
            Text(
              state.lastMonthApprovedReward.toPrice(state.currency),
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
            ),
            'Reward approved last month',
            'Saldo yang akan dibawa ke bulan berikutnya. Apabila tidak ada maka akan bernilai 0',
          ),
        ],
      ),
    );
  }

  Widget _buildCardContent(Widget data, String description, String dialogContents) {
    return Padding(
      padding: EdgeInsets.all(12.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          data,
          Row(
            spacing: 2.r,
            children: [
              Text(
                description,
                style: Theme.of(
                  context,
                ).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500, color: const Color(0xFF767676)),
              ),
              GestureDetector(
                onTap: () {
                  showDescription(context, description, dialogContents);
                },
                child: Icon(Icons.help_outline, size: 16.r, color: const Color(0xFF767676)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPerformance(ReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Performance', () {
          Modular.to.pushNamed('/report/performance-monthly');
        }),
        _buildPerformanceChart(state),
      ],
    );
  }

  Widget _buildPerformanceChart(ReportState state) {
    if (state.performanceMonthlyData.isNotEmpty) {
      return _buildPerformanceChartFromMonthlyData(state.performanceMonthlyData);
    } else {
      return _buildPerformanceChartFromMaps(state.currentOneYearClickCount, state.currentOneYearConversionCount);
    }
  }

  Widget _buildPerformanceChartFromMonthlyData(List<PerformanceMonthlyReportData> performanceData) {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month, 1);

    final filteredData =
        performanceData.where((data) {
          if (data.month == null) return false;
          try {
            final DateTime monthDate = data.month!.toDateTime(yearMonthFormat);
            final DateTime monthKey = DateTime(monthDate.year, monthDate.month, 1);
            return !monthKey.isAfter(currentMonth);
          } catch (e) {
            return false;
          }
        }).toList();

    // Sort by month
    filteredData.sort((a, b) {
      try {
        final aDate = a.month!.toDateTime(yearMonthFormat);
        final bDate = b.month!.toDateTime(yearMonthFormat);
        return aDate.compareTo(bDate);
      } catch (e) {
        return 0;
      }
    });

    final List<String> labels =
        filteredData
            .map((data) => DateFormat(abbreviateMonthYearFormat).format(data.month!.toDateTime(yearMonthFormat)))
            .toList();
    final List<num> leftCounts = filteredData.map((e) => e.clicks as num).toList();
    final List<num> rightCounts = filteredData.map((e) => e.conversions as num).toList();

    final chartData = DualAxisChartData(
      labels: labels,
      leftAxisData: leftCounts,
      rightAxisData: rightCounts,
      leftAxisLabel: 'Clicks',
      rightAxisLabel: 'Conversions',
    );

    const chartConfig = DualAxisChartConfig(
      showLegend: true,
      aspectRatio: 1.4,
      normalHeight: 250.0,
      emptyHeight: 200.0,
      showContainer: true,
      emptyTitle: 'Clicks & Conversions',
      showEmptyTitle: true,
    );

    return DualAxisLineChart(data: chartData, config: chartConfig);
  }

  Widget _buildPerformanceChartFromMaps(
    Map<DateTime, int> currentOneYearClickCount,
    Map<DateTime, int> currentOneYearConversionCount,
  ) {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month, 1);

    List<MapEntry<DateTime, int>> filteredClickEntries;
    List<MapEntry<DateTime, int>> filteredConversionEntries;

    if (currentOneYearClickCount.isEmpty || currentOneYearConversionCount.isEmpty) {
      filteredClickEntries =
          List.generate(12, (index) {
            final date = DateTime(now.year, now.month - index, 1);
            return MapEntry(date, 0);
          }).reversed.toList();
      filteredConversionEntries =
          List.generate(12, (index) {
            final date = DateTime(now.year, now.month - index, 1);
            return MapEntry(date, 0);
          }).reversed.toList();
    } else {
      filteredClickEntries =
          currentOneYearClickCount.entries.where((entry) => !entry.key.isAfter(currentMonth)).toList()
            ..sort((a, b) => a.key.compareTo(b.key));
      filteredConversionEntries =
          currentOneYearConversionCount.entries.where((entry) => !entry.key.isAfter(currentMonth)).toList()
            ..sort((a, b) => a.key.compareTo(b.key));
    }

    final List<String> labels = filteredClickEntries.map((data) => DateFormat('MMM y').format(data.key)).toList();
    final List<int> clickCount = filteredClickEntries.map((e) => e.value).toList();
    final List<int> conversionCount = filteredConversionEntries.map((e) => e.value).toList();

    final chartData = DualAxisChartData(
      labels: labels,
      leftAxisData: clickCount.map((e) => e as num).toList(),
      rightAxisData: conversionCount.map((e) => e as num).toList(),
      leftAxisLabel: 'Clicks',
      rightAxisLabel: 'Conversions',
    );

    const chartConfig = DualAxisChartConfig(
      showLegend: true,
      aspectRatio: 1.4,
      normalHeight: 200.0,
      emptyHeight: 180.0,
      showContainer: true,
      emptyTitle: 'Clicks & Conversions',
      showEmptyTitle: true,
    );

    return DualAxisLineChart(data: chartData, config: chartConfig);
  }
}
