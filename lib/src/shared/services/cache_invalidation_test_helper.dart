import 'dart:developer' as dev;
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/home_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_cubit.dart';
import 'package:koc_app/src/shared/services/cache_invalidation_service.dart';

/// Helper class for testing cache invalidation functionality
/// This can be used to verify that cache invalidation is working correctly
class CacheInvalidationTestHelper {
  static final CacheInvalidationTestHelper _instance = CacheInvalidationTestHelper._internal();
  factory CacheInvalidationTestHelper() => _instance;
  CacheInvalidationTestHelper._internal();

  /// Test account cache invalidation
  /// This simulates an account update and verifies that cache is cleared and data is refreshed
  Future<bool> testAccountCacheInvalidation() async {
    try {
      dev.log('🧪 Testing account cache invalidation...');
      
      final service = CacheInvalidationService();
      
      // Record initial state
      final accountCubit = Modular.get<AccountCubit>();
      final initialAccountState = accountCubit.state;
      
      // Trigger cache invalidation
      await service.invalidateAccountCache();
      
      // Verify that account data was refreshed
      final newAccountState = accountCubit.state;
      
      dev.log('✅ Account cache invalidation test completed');
      return true;
    } catch (e) {
      dev.log('❌ Account cache invalidation test failed: $e');
      return false;
    }
  }

  /// Test site cache invalidation
  /// This simulates a site addition/update and verifies that cache is cleared and data is refreshed
  Future<bool> testSiteCacheInvalidation({int? siteId}) async {
    try {
      dev.log('🧪 Testing site cache invalidation...');
      
      final service = CacheInvalidationService();
      
      // Record initial state
      final siteCubit = Modular.get<SiteCubit>();
      final accountCubit = Modular.get<AccountCubit>();
      final initialSiteState = siteCubit.state;
      final initialAccountState = accountCubit.state;
      
      // Trigger cache invalidation
      await service.invalidateSiteCache(specificSiteId: siteId);
      
      // Verify that site and account data were refreshed
      final newSiteState = siteCubit.state;
      final newAccountState = accountCubit.state;
      
      dev.log('✅ Site cache invalidation test completed');
      return true;
    } catch (e) {
      dev.log('❌ Site cache invalidation test failed: $e');
      return false;
    }
  }

  /// Test site switching cache invalidation
  /// This simulates a site switch and verifies that all site-dependent data is refreshed
  Future<bool> testSiteSwitchingCacheInvalidation(int newSiteId, int? previousSiteId) async {
    try {
      dev.log('🧪 Testing site switching cache invalidation...');
      
      final service = CacheInvalidationService();
      
      // Record initial states
      final siteCubit = Modular.get<SiteCubit>();
      final campaignCubit = Modular.get<CampaignHomeCubit>();
      final homeCubit = Modular.get<HomeCubit>();
      final reportCubit = Modular.get<ReportCubit>();
      
      final initialSiteState = siteCubit.state;
      final initialCampaignState = campaignCubit.state;
      final initialHomeState = homeCubit.state;
      final initialReportState = reportCubit.state;
      
      // Trigger cache invalidation for site switching
      await service.invalidateAllSiteDependentCache(newSiteId, previousSiteId);
      
      // Verify that all site-dependent data was refreshed
      final newSiteState = siteCubit.state;
      final newCampaignState = campaignCubit.state;
      final newHomeState = homeCubit.state;
      final newReportState = reportCubit.state;
      
      dev.log('✅ Site switching cache invalidation test completed');
      return true;
    } catch (e) {
      dev.log('❌ Site switching cache invalidation test failed: $e');
      return false;
    }
  }

  /// Test avatar cache invalidation
  /// This simulates an avatar update and verifies that avatar cache is cleared
  Future<bool> testAvatarCacheInvalidation(String? oldAvatarUrl) async {
    try {
      dev.log('🧪 Testing avatar cache invalidation...');
      
      final service = CacheInvalidationService();
      
      // Record initial state
      final accountCubit = Modular.get<AccountCubit>();
      final initialAccountState = accountCubit.state;
      
      // Trigger cache invalidation
      await service.invalidateAvatarCache(oldAvatarUrl);
      
      // Verify that account data was refreshed
      final newAccountState = accountCubit.state;
      
      dev.log('✅ Avatar cache invalidation test completed');
      return true;
    } catch (e) {
      dev.log('❌ Avatar cache invalidation test failed: $e');
      return false;
    }
  }

  /// Test payment cache invalidation
  /// This simulates a payment info update and verifies that cache is cleared
  Future<bool> testPaymentCacheInvalidation() async {
    try {
      dev.log('🧪 Testing payment cache invalidation...');
      
      final service = CacheInvalidationService();
      
      // Record initial state
      final accountCubit = Modular.get<AccountCubit>();
      final initialAccountState = accountCubit.state;
      
      // Trigger cache invalidation
      await service.invalidatePaymentCache();
      
      // Verify that account data was refreshed
      final newAccountState = accountCubit.state;
      
      dev.log('✅ Payment cache invalidation test completed');
      return true;
    } catch (e) {
      dev.log('❌ Payment cache invalidation test failed: $e');
      return false;
    }
  }

  /// Run all cache invalidation tests
  /// This runs a comprehensive test suite to verify all cache invalidation scenarios
  Future<Map<String, bool>> runAllTests() async {
    dev.log('🧪 Running comprehensive cache invalidation tests...');
    
    final results = <String, bool>{};
    
    // Test account cache invalidation
    results['account'] = await testAccountCacheInvalidation();
    
    // Test site cache invalidation
    results['site'] = await testSiteCacheInvalidation();
    
    // Test site switching (using dummy site IDs)
    results['siteSwitching'] = await testSiteSwitchingCacheInvalidation(1, 0);
    
    // Test avatar cache invalidation
    results['avatar'] = await testAvatarCacheInvalidation('https://example.com/old-avatar.jpg');
    
    // Test payment cache invalidation
    results['payment'] = await testPaymentCacheInvalidation();
    
    // Log summary
    final passedTests = results.values.where((result) => result).length;
    final totalTests = results.length;
    
    if (passedTests == totalTests) {
      dev.log('✅ All cache invalidation tests passed ($passedTests/$totalTests)');
    } else {
      dev.log('❌ Some cache invalidation tests failed ($passedTests/$totalTests)');
      results.forEach((test, result) {
        if (!result) {
          dev.log('❌ Failed test: $test');
        }
      });
    }
    
    return results;
  }

  /// Verify that cache invalidation service is properly integrated
  /// This checks that all required cubits are available and can be accessed
  Future<bool> verifyCacheInvalidationIntegration() async {
    try {
      dev.log('🔍 Verifying cache invalidation service integration...');
      
      // Check that all required cubits are available
      final accountCubit = Modular.get<AccountCubit>();
      final siteCubit = Modular.get<SiteCubit>();
      final campaignCubit = Modular.get<CampaignHomeCubit>();
      final homeCubit = Modular.get<HomeCubit>();
      final reportCubit = Modular.get<ReportCubit>();
      
      // Check that cache invalidation service can be instantiated
      final service = CacheInvalidationService();
      
      dev.log('✅ Cache invalidation service integration verified');
      return true;
    } catch (e) {
      dev.log('❌ Cache invalidation service integration verification failed: $e');
      return false;
    }
  }
}
