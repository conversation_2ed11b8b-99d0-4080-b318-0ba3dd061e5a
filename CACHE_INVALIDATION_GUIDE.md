# Cache Invalidation and Data Refresh Guide

This guide explains how to use the cache invalidation system in KOC-App to ensure data consistency when user account information or site data changes.

## Overview

The cache invalidation system provides centralized methods for clearing cache and refreshing data across the app when:
1. User account information is updated (name, email, phone, address, avatar, payment info)
2. Sites are added, updated, or deleted
3. User switches between sites

## Core Components

### 1. CacheInvalidationService

The main service that handles all cache invalidation scenarios:

```dart
import 'package:koc_app/src/shared/services/cache_invalidation_service.dart';

final service = CacheInvalidationService();
```

### 2. Available Methods

#### Account Updates
```dart
// After updating account information (name, email, phone, address)
await CacheInvalidationService().invalidateAccountCache();

// After updating avatar
await CacheInvalidationService().invalidateAvatarCache(oldAvatarUrl);

// After updating payment information
await CacheInvalidationService().invalidatePaymentCache();
```

#### Site Management
```dart
// After adding or updating a site
await CacheInvalidationService().invalidateSiteCache(specificSiteId: siteId);

// When switching sites
await CacheInvalidationService().invalidateAllSiteDependentCache(newSiteId, previousSiteId);
```

## Implementation Examples

### 1. Account Settings Updates

Already implemented in `AccountSettingsCubit`:

```dart
Future<bool> updateName(String firstName, String lastName) async {
  try {
    await _accountRepository.updateName(firstName, lastName);
    emit(state.copyWith(firstName: firstName, lastName: lastName));
    
    // Invalidate account cache to refresh data across the app
    await CacheInvalidationService().invalidateAccountCache();
    
    return true;
  } catch (e) {
    handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    return false;
  }
}
```

### 2. Site Addition/Updates

Already implemented in `TrafficSourcesCubit`:

```dart
Future<bool> upsertSocialInfo(SocialInfo socialInfo) async {
  try {
    final siteId = await _accountRepository.updateTrafficSources(updatedSource);

    // Use cache invalidation service for site updates
    await CacheInvalidationService().invalidateSiteCache(specificSiteId: siteId);

    emit(state.copyWith(id: siteId, ...));
    return true;
  } catch (e) {
    handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    return false;
  }
}
```

### 3. Site Switching

Already implemented in `SiteCubit`:

```dart
Future<void> setCurrentSiteId(int siteId) async {
  final previousSiteId = state.currentSiteId;
  emit(state.copyWith(isSwitchingSite: true));

  try {
    // Use cache invalidation service for comprehensive site switching
    await CacheInvalidationService().invalidateAllSiteDependentCache(siteId, previousSiteId);

    await commonCubit.sharedPreferencesService.setCurrentSiteId(siteId);
    emit(state.copyWith(currentSiteId: siteId, isSwitchingSite: true));

    // Continue with other refresh logic...
  } finally {
    emit(state.copyWith(isSwitchingSite: false));
  }
}
```

## Cache Refresh Mixin

For pages that need to automatically refresh when data changes:

```dart
import 'package:koc_app/src/shared/mixin/cache_refresh_mixin.dart';

class MyPageState extends BasePageState<MyPage, MyCubit> with CacheRefreshMixin {
  
  @override
  Future<void> onAccountDataChanged() async {
    // Refresh page data when account changes
    await cubit.refreshData();
  }

  @override
  Future<void> onSiteDataChanged() async {
    // Refresh page data when sites change
    await cubit.refreshSiteData();
  }

  @override
  Future<void> onCurrentSiteChanged(int newSiteId, int previousSiteId) async {
    // Refresh page data when current site changes
    await cubit.refreshForSite(newSiteId);
  }
}
```

## Cache Refresh Wrapper

For wrapping widgets that need to respond to data changes:

```dart
import 'package:koc_app/src/shared/mixin/cache_refresh_mixin.dart';

CacheRefreshWrapper(
  onAccountChanged: () {
    // Handle account data changes
    cubit.refreshAccountData();
  },
  onSiteChanged: () {
    // Handle site list changes
    cubit.refreshSiteData();
  },
  onCurrentSiteChanged: (newSiteId, previousSiteId) {
    // Handle site switching
    cubit.refreshForSite(newSiteId);
  },
  child: MyWidget(),
)
```

## Testing

Use the test helper to verify cache invalidation is working:

```dart
import 'package:koc_app/src/shared/services/cache_invalidation_test_helper.dart';

// Run all tests
final results = await CacheInvalidationTestHelper().runAllTests();

// Test specific scenarios
await CacheInvalidationTestHelper().testAccountCacheInvalidation();
await CacheInvalidationTestHelper().testSiteCacheInvalidation();
```

## What Gets Cleared

### Account Cache Invalidation
- `/v3/publishers/me/account`
- `/v3/publishers/me/avatar`
- `/v3/publishers/me/sites`
- `/v3/publishers/me/profile`

### Avatar Cache Invalidation
- `/v3/publishers/me/avatar`
- `/v3/publishers/me/account`
- Old avatar image from image cache

### Site Cache Invalidation
- `/v3/publishers/me/sites`
- `/v3/publishers/me/account`
- Site-specific endpoints for the given site ID

### Site Switching Cache Invalidation
- All site-specific cache for both old and new sites
- Reports, campaigns, vouchers, and other site-dependent data
- Campaign count summaries and featured campaigns

## Best Practices

1. **Always call cache invalidation after successful data updates**
2. **Use the appropriate method for the type of data being updated**
3. **Handle errors gracefully - cache invalidation should not break the user flow**
4. **Test cache invalidation in development to ensure it's working correctly**
5. **Use the cache refresh mixin for pages that display cached data**

## Troubleshooting

If data is not refreshing after updates:

1. Check that cache invalidation is being called after successful API updates
2. Verify that the correct cache invalidation method is being used
3. Use the test helper to verify integration
4. Check console logs for cache invalidation debug messages
5. Ensure all required cubits are properly registered in the dependency injection system
