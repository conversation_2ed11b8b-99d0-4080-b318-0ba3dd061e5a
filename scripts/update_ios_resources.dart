import 'dart:io';

/// A simple script to update iOS Info.plist with values from .env file
/// Run this script with: dart scripts/update_ios_resources.dart
void main() async {
  // Read the .env file
  final envFile = File('.env');
  if (!await envFile.exists()) {
    print('Error: .env file not found');
    exit(1);
  }

  final envContent = await envFile.readAsString();
  final envLines = envContent.split('\n');

  // Extract Facebook & Google values from .env
  String? facebookAppId;
  String? facebookClientToken;
  String? googleReversedClientId;

  for (final line in envLines) {
    if (line.trim().startsWith('#') || line.trim().isEmpty) continue;

    final parts = line.split('=');
    if (parts.length < 2) continue;
    final key = parts[0].trim();
    final value = parts.sublist(1).join('=').trim();

    if (key == 'FACEBOOK_APP_ID') {
      facebookAppId = value;
    } else if (key == 'FACEBOOK_CLIENT_TOKEN') {
      facebookClientToken = value;
    } else if (key == 'GOOGLE_REVERSED_CLIENT_ID_IOS') {
      googleReversedClientId = value;
    }
  }

  if (facebookAppId == null || facebookClientToken == null) {
    print('Warning: Facebook App ID or Client Token not found in .env file. Skipping Facebook update.');
  }
  if (googleReversedClientId == null) {
    print('Warning: GOOGLE_REVERSED_CLIENT_ID_IOS not found in .env file. Skipping Google update.');
  }

  // Read the Info.plist file
  final plistFile = File('ios/Runner/Info.plist');
  if (!await plistFile.exists()) {
    print('Error: Info.plist file not found');
    exit(1);
  }

  final plistContent = await plistFile.readAsString();

  // Update values in Info.plist
  var updatedContent = plistContent;
  bool facebookUpdated = false;
  bool googleUpdated = false;

  // Update Facebook App ID
  if (facebookAppId != null) {
    final appIdRegex = RegExp(r'<key>FacebookAppID</key>\s*<string>([^<]*)</string>');
    if (appIdRegex.hasMatch(updatedContent)) {
      updatedContent = updatedContent.replaceAllMapped(
          appIdRegex, (match) => '<key>FacebookAppID</key>\n\t\t<string>$facebookAppId</string>');
      facebookUpdated = true;
    }

    // Update Facebook URL Scheme (ensure it's within CFBundleURLSchemes array for robustness)
    // This regex looks for <string>fbANYTHING</string> inside a <key>CFBundleURLSchemes</key> context
    final fbSchemeRegex =
        RegExp(r'(<key>CFBundleURLSchemes</key>\s*<array>(?:[^<]|<(?!\/array>))*<string>)fb[^<]*(</string>)');
    if (fbSchemeRegex.hasMatch(updatedContent)) {
      updatedContent = updatedContent.replaceAllMapped(
          fbSchemeRegex, (match) => '${match.group(1)}fb$facebookAppId${match.group(2)}');
      facebookUpdated = true;
    } else {
      // Fallback for simpler structure if the above doesn't match
      final simpleFbSchemeRegex = RegExp(r'<string>fb[^<]*</string>');
      if (simpleFbSchemeRegex.hasMatch(updatedContent)) {
        updatedContent =
            updatedContent.replaceAllMapped(simpleFbSchemeRegex, (match) => '<string>fb$facebookAppId</string>');
        facebookUpdated = true;
      }
    }
  }

  // Update Facebook Client Token
  if (facebookClientToken != null) {
    final tokenRegex = RegExp(r'<key>FacebookClientToken</key>\s*<string>([^<]*)</string>');
    if (tokenRegex.hasMatch(updatedContent)) {
      updatedContent = updatedContent.replaceAllMapped(
          tokenRegex, (match) => '<key>FacebookClientToken</key>\n\t\t<string>$facebookClientToken</string>');
      facebookUpdated = true;
    }
  }

  // Update Google Reversed Client ID (URL Scheme)
  if (googleReversedClientId != null) {
    // This regex looks for <string>com.googleusercontent.apps.ANYTHING</string> inside a <key>CFBundleURLSchemes</key> context
    final googleSchemeRegex = RegExp(
        r'(<key>CFBundleURLSchemes</key>\s*<array>(?:[^<]|<(?!\/array>))*<string>)com\.googleusercontent\.apps\.[^<]*(</string>)');
    if (googleSchemeRegex.hasMatch(updatedContent)) {
      updatedContent = updatedContent.replaceAllMapped(
          googleSchemeRegex, (match) => '${match.group(1)}$googleReversedClientId${match.group(2)}');
      googleUpdated = true;
    } else {
      // Fallback for simpler structure if the above doesn't match or if it's a direct string replacement target
      final simpleGoogleSchemeRegex = RegExp(r'<string>com\.googleusercontent\.apps\.[^<]*</string>');
      if (simpleGoogleSchemeRegex.hasMatch(updatedContent)) {
        updatedContent = updatedContent.replaceAllMapped(
            simpleGoogleSchemeRegex, (match) => '<string>$googleReversedClientId</string>');
        googleUpdated = true;
      } else {
        // Attempt to find a placeholder string if specific pattern not found
        final placeholderGoogleSchemeRegex = RegExp(r'<string>REPLACE_WITH_GOOGLE_REVERSED_CLIENT_ID</string>');
        if (placeholderGoogleSchemeRegex.hasMatch(updatedContent)) {
          updatedContent =
              updatedContent.replaceAll(placeholderGoogleSchemeRegex, '<string>$googleReversedClientId</string>');
          googleUpdated = true;
        }
      }
    }
  }

  // Write the updated content back to Info.plist
  if (facebookUpdated || googleUpdated) {
    await plistFile.writeAsString(updatedContent);
    if (facebookUpdated) {
      print('Successfully updated iOS resources with Facebook values from .env');
    }
    if (googleUpdated) {
      print('Successfully updated iOS resources with Google values from .env');
    }
  } else {
    print('No iOS resources updated.');
  }
}
